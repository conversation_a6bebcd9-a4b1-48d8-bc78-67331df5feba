package main

import (
	"os"
	"strconv"
	"strings"
)

// SupabaseConfig 存储Supabase的配置信息
type SupabaseConfig struct {
	ID  string
	URL string
	Key string
}

// OpenAIConfig 存储OpenAI API的配置信息
type OpenAIConfig struct {
	OpenAIBaseURL    string
	OpenAIAPIKey     string
	BytedanceBaseURL string
	BytedanceAPIKey  string
}

// 获取Supabase ID
func GetSupabaseID() string {
	// 从环境变量中读取Supabase ID
	return os.Getenv("SUPABASE_ID")
}

// 获取Supabase URL
func GetSupabaseURL() string {
	// 从环境变量中读取Supabase URL
	return os.Getenv("SUPABASE_URL")
}

// 获取Supabase Key
func GetSupabaseKey() string {
	// 从环境变量中读取Supabase匿名密钥
	if os.Getenv("SUPABASE_SERVICE_ROLE_KEY") != "" {
		return os.Getenv("SUPABASE_SERVICE_ROLE_KEY")
	}
	return os.Getenv("SUPABASE_KEY")
}

// 获取Supabase配置
func GetSupabaseConfig() SupabaseConfig {
	// 从环境变量中读取Supabase配置
	return SupabaseConfig{
		ID:  GetSupabaseID(),
		URL: GetSupabaseURL(),
		Key: GetSupabaseKey(),
	}
}

// 获取OpenAI Base URL
func GetOpenAIBaseURL() string {
	// 从环境变量中读取OpenAI Base URL
	openaiBaseURL := os.Getenv("OPENAI_BASE_URL")
	// 如果环境变量不存在，使用默认值
	if openaiBaseURL == "" {
		openaiBaseURL = "https://ark.cn-beijing.volces.com/api/v3"
	}
	return openaiBaseURL
}

// 获取OpenAI API Key
func GetOpenAIAPIKey() string {
	// 从环境变量中读取OpenAI API Key
	return os.Getenv("OPENAI_API_KEY")
}

// 获取字节跳动 Base URL
func GetBytedanceBaseURL() string {
	// 从环境变量中读取字节跳动 Base URL
	return os.Getenv("BYTEDANCE_BASE_URL")
}

// 获取字节跳动 API Key
func GetBytedanceAPIKey() string {
	// 从环境变量中读取字节跳动 API Key
	return os.Getenv("BYTEDANCE_API_KEY")
}



// 获取OpenAI API配置
func GetOpenAIConfig() OpenAIConfig {
	return OpenAIConfig{
		OpenAIBaseURL:    GetOpenAIBaseURL(),
		OpenAIAPIKey:     GetOpenAIAPIKey(),
		BytedanceBaseURL: GetBytedanceBaseURL(),
		BytedanceAPIKey:  GetBytedanceAPIKey(),
	}
}

// GetAdminsList 获取管理员ID列表
// 从环境变量ADMIN_IDS获取，多个ID用逗号分隔
func GetAdminsList() []string {
	adminIDs := os.Getenv("ADMIN_IDS")
	if adminIDs == "" {
		// 如果环境变量未设置，返回默认管理员列表，方便开发测试
		return []string{"333323c8-b917-403c-8d63-8e6e6f646522", "dd05b2aa-b079-437a-8db9-5345835f68aa"}
	}
	// 按逗号分割字符串
	return strings.Split(adminIDs, ",")
}

// AppVersionConfig 存储应用版本的配置信息
type AppVersionConfig struct {
	Version     string
	DownloadURL string
	ForceUpdate bool
	UpdateLog   string
}

// VolcSTSConfig 火山引擎STS配置
type VolcSTSConfig struct {
	STSBaseURL       string
	AccessKey        string
	SecretKey        string
	RoleArn          string
	Bucket           string
	Endpoint         string
	IntranetEndpoint string
	Region           string
}

// GetAppVersion 获取应用版本信息
func GetAppVersion() AppVersionConfig {
	return AppVersionConfig{
		Version:     getEnvWithDefault("APP_VERSION", "v0.0.0"),
		DownloadURL: getEnvWithDefault("APP_DOWNLOAD_URL", "https://shanzhulab.cn/"),
		ForceUpdate: getEnvBoolWithDefault("APP_FORCE_UPDATE", false),
		UpdateLog:   getEnvWithDefault("APP_UPDATE_LOG", "在山竹阅卷官网https://shanzhulab.cn或者公众号获取最新版本下载链接"),
	}
}

// GetVolcSTSBaseURL 获取火山引擎STS服务基础URL
func GetVolcSTSBaseURL() string {
	// 从环境变量中读取火山引擎STS服务基础URL
	return getEnvWithDefault("VOLC_STS_URL", "https://open.volcengineapi.com")
}

// GetVolcAccessKey 获取火山引擎访问密钥ID
func GetVolcAccessKey() string {
	// 从环境变量中读取火山引擎访问密钥ID
	return getEnvWithDefault("VOLC_ACCESS_KEY", "")
}

// GetVolcSecretKey 获取火山引擎访问密钥
func GetVolcSecretKey() string {
	// 从环境变量中读取火山引擎访问密钥
	return getEnvWithDefault("VOLC_SECRET_KEY", "")
}

// GetVolcRoleArn 获取火山引擎角色ARN
func GetVolcRoleArn() string {
	// 从环境变量中读取火山引擎角色ARN
	return getEnvWithDefault("VOLC_ROLE_ARN", "")
}

// GetVolcBucket 获取火山引擎TOS存储桶名称
func GetVolcBucket() string {
	// 从环境变量中读取火山引擎TOS存储桶名称
	return getEnvWithDefault("VOLC_BUCKET", "")
}

// GetVolcEndpoint 获取火山引擎TOS服务端点
func GetVolcEndpoint() string {
	// 从环境变量中读取火山引擎TOS服务端点
	return getEnvWithDefault("VOLC_ENDPOINT", "tos-cn-beijing.volces.com")
}

// GetVolcIntranetEndpoint 获取火山引擎TOS内网服务端点
func GetVolcIntranetEndpoint() string {
	// 从环境变量中读取火山引擎TOS内网服务端点
	return getEnvWithDefault("VOLC_INTRANET_ENDPOINT", "tos-cn-beijing.ivolces.com")
}

// GetVolcRegion 获取火山引擎TOS存储桶所在区域
func GetVolcRegion() string {
	// 从环境变量中读取火山引擎TOS存储桶所在区域
	return getEnvWithDefault("VOLC_REGION", "cn-beijing")
}

// GetVolcSTSConfig 获取火山引擎STS配置
func GetVolcSTSConfig() VolcSTSConfig {
	return VolcSTSConfig{
		STSBaseURL:       GetVolcSTSBaseURL(),
		AccessKey:        GetVolcAccessKey(),
		SecretKey:        GetVolcSecretKey(),
		RoleArn:          GetVolcRoleArn(),
		Bucket:           GetVolcBucket(),
		Endpoint:         GetVolcEndpoint(),
		IntranetEndpoint: GetVolcIntranetEndpoint(),
		Region:           GetVolcRegion(),
	}
}

// getEnvWithDefault 获取环境变量，如果不存在则返回默认值
func getEnvWithDefault(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// getEnvBoolWithDefault 获取布尔类型的环境变量，如果不存在则返回默认值
func getEnvBoolWithDefault(key string, defaultValue bool) bool {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	value = strings.ToLower(value)
	return value == "true" || value == "1" || value == "yes"
}

// GetFeishuWebhookURL 获取飞书Webhook URL
func GetFeishuWebhookURL() string {
	// 从环境变量中读取飞书Webhook URL
	return getEnvWithDefault("FEISHU_WEBHOOK_URL", "")
}

// GetSMTPHost 获取SMTP服务器地址
func GetSMTPHost() string {
	// 从环境变量中读取SMTP服务器地址
	return getEnvWithDefault("SMTP_HOST", "")
}

// GetSMTPPort 获取SMTP服务器端口
func GetSMTPPort() int {
	// 从环境变量中读取SMTP服务器端口
	portStr := getEnvWithDefault("SMTP_PORT", "465")
	port, err := strconv.Atoi(portStr)
	if err != nil {
		return 587 // 默认端口
	}
	return port
}

// GetSMTPUser 获取SMTP用户名
func GetSMTPUser() string {
	// 从环境变量中读取SMTP用户名
	return getEnvWithDefault("SMTP_USER", "")
}

// GetSMTPPassword 获取SMTP密码
func GetSMTPPassword() string {
	// 从环境变量中读取SMTP密码
	return getEnvWithDefault("SMTP_PASSWORD", "")
}

// GetSMTPFrom 获取SMTP发件人
func GetSMTPFrom() string {
	// 从环境变量中读取SMTP发件人
	return getEnvWithDefault("SMTP_FROM", "")
}

// SMTPConfig 存储SMTP的配置信息
type SMTPConfig struct {
	Host     string
	Port     int
	User     string
	Password string
	From     string
}

// GetSMTPConfig 获取SMTP配置
func GetSMTPConfig() SMTPConfig {
	return SMTPConfig{
		Host:     GetSMTPHost(),
		Port:     GetSMTPPort(),
		User:     GetSMTPUser(),
		Password: GetSMTPPassword(),
		From:     GetSMTPFrom(),
	}
}

// GetDBHost 获取数据库主机地址
func GetDBHost() string {
	return getEnvWithDefault("DB_HOST", "")
}

// GetDBPort 获取数据库端口
func GetDBPort() string {
	return getEnvWithDefault("DB_PORT", "5432")
}

// GetDBUser 获取数据库用户名
func GetDBUser() string {
	return getEnvWithDefault("DB_USER", "aig_user")
}

// GetDBPassword 获取数据库密码
func GetDBPassword() string {
	return getEnvWithDefault("DB_PASSWORD", "")
}

// GetDBName 获取数据库名称
func GetDBName() string {
	return getEnvWithDefault("DB_NAME", "aig")
}