package main

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestLoginHandler(t *testing.T) {
	// 跳过实际测试，因为它需要真实的Supabase凭据
	t.Skip("需要真实的Supabase凭据")

	// 获取配置
	config := GetSupabaseConfig()

	// 创建Auth客户端
	authClient, err := NewAuthClient(config)
	if err != nil {
		t.Fatalf("初始化Auth客户端失败: %v", err)
	}

	// 创建登录请求
	loginReq := LoginRequest{
		Email:    "<EMAIL>", // 替换为真实的测试邮箱
		Password: "password123",      // 替换为真实的测试密码
	}
	reqBody, err := json.Marshal(loginReq)
	if err != nil {
		t.Fatalf("序列化请求体失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", "/api/v1/auth/login", bytes.NewBuffer(reqBody))
	if err != nil {
		t.Fatalf("创建请求失败: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// 创建响应记录器
	rr := httptest.NewRecorder()

	// 调用处理程序
	handler := http.HandlerFunc(authClient.LoginHandler)
	handler.ServeHTTP(rr, req)

	// 检查状态码
	if status := rr.Code; status != http.StatusOK {
		t.Errorf("处理程序返回了错误的状态码: 得到 %v 期望 %v", status, http.StatusOK)
	}

	// 解析响应
	var resp LoginResponse
	if err := json.NewDecoder(rr.Body).Decode(&resp); err != nil {
		t.Fatalf("解析响应失败: %v", err)
	}

	// 验证响应
	if resp.AccessToken == "" {
		t.Error("响应中没有访问令牌")
	}
	if resp.RefreshToken == "" {
		t.Error("响应中没有刷新令牌")
	}
}
