package main

import (
	"encoding/json"
	"net/http"
)

// DepositRequest 表示充值请求的结构
type DepositRequest struct {
	Amount      int            `json:"amount"`
	Description string         `json:"description,omitempty"`
	Metadata    map[string]any `json:"metadata,omitempty"`
}

// BalanceResponse 表示余额响应的结构
type BalanceResponse struct {
	Balance int `json:"balance"`
}

// DepositResponse 表示充值响应的结构
type DepositResponse struct {
	Success bool `json:"success"`
	Balance int  `json:"balance"`
	Amount  int  `json:"amount"`
}

// TransactionsResponse 表示交易记录响应的结构
type TransactionsResponse struct {
	Transactions []Transaction `json:"transactions"`
}

// HandleGetBalance 处理查询余额请求
func HandleGetBalance(w http.ResponseWriter, r *http.Request) {
	// 只接受GET请求
	if r.Method != http.MethodGet {
		http.Error(w, "方法不允许", http.StatusMethodNotAllowed)
		return
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "未授权", http.StatusUnauthorized)
		return
	}
	// 确保钱包存在
	balance, err := pg.GetUserBalance(user.ID.String())
	if err != nil {
		http.Error(w, "获取余额失败", http.StatusInternalServerError)
		return
	}
	// 返回余额
	resp := BalanceResponse{
		Balance: balance,
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(resp)
}

// TransactionsRequest
type TransactionsRequest struct {
	Limit  int `json:"limit"`
	Offset int `json:"offset"`
}

// HandleGetTransactions 处理查询交易记录请求
func HandleGetTransactions(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "", http.StatusMethodNotAllowed)
		return
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "", http.StatusUnauthorized)
		return
	}
	// 解析请求体
	var req TransactionsRequest = TransactionsRequest{}
	json.NewDecoder(r.Body).Decode(&req)
	// 默认值
	if req.Limit <= 0 {
		req.Limit = 20
	}
	if req.Offset < 0 {
		req.Offset = 0
	}
	// 获取交易记录
	transactions, err := pg.GetUserTransactionRecords(user.ID.String(), req.Limit, req.Offset)
	if err != nil {
		http.Error(w, "failed to get transactions", http.StatusInternalServerError)
		return
	}
	// 返回交易记录
	resp := TransactionsResponse{
		Transactions: transactions,
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(resp)
}
