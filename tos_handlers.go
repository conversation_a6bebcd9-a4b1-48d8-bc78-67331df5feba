package main

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/volcengine/volcengine-go-sdk/service/sts"
	"github.com/volcengine/volcengine-go-sdk/volcengine"
	"github.com/volcengine/volcengine-go-sdk/volcengine/credentials"
	"github.com/volcengine/volcengine-go-sdk/volcengine/session"
)

// TOSCredentials 表示TOS临时访问凭证
type TOSCredentials struct {
	AccessKeyID     string `json:"access_key_id"`
	SecretAccessKey string `json:"secret_access_key"`
	SessionToken    string `json:"session_token"`
	Expiration      string `json:"expiration"`
}

// TOSCredentialsResponse 表示TOS临时访问凭证的响应
type TOSCredentialsResponse struct {
	Success          bool           `json:"success"`
	Credentials      TOSCredentials `json:"credentials"`
	Bucket           string         `json:"bucket"`
	Endpoint         string         `json:"endpoint"`
	IntranetEndpoint string         `json:"intranet_endpoint"`
	Region           string         `json:"region"`
	Message          string         `json:"message,omitempty"`
}

// validateVolcSTSConfig 验证火山引擎STS配置
func validateVolcSTSConfig(config VolcSTSConfig) error {
	if config.AccessKey == "" {
		return fmt.Errorf("VOLC_ACCESS_KEY环境变量未设置")
	}
	if config.SecretKey == "" {
		return fmt.Errorf("VOLC_SECRET_KEY环境变量未设置")
	}
	if config.RoleArn == "" {
		return fmt.Errorf("VOLC_ROLE_ARN环境变量未设置")
	}
	return nil
}

// HandleGetTOSCredentials 处理获取TOS临时访问凭证的请求
func HandleGetTOSCredentials(w http.ResponseWriter, r *http.Request) {
	// 只接受GET请求
	if r.Method != http.MethodGet {
		http.Error(w, "方法不允许", http.StatusMethodNotAllowed)
		return
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "未授权", http.StatusUnauthorized)
		return
	}
	// 获取火山引擎STS配置
	stsConfig := GetVolcSTSConfig()
	// 验证配置
	if err := validateVolcSTSConfig(stsConfig); err != nil {
		http.Error(w, "STS配置无效: "+err.Error(), http.StatusInternalServerError)
		return
	}
	// 使用SDK创建会话
	sess, err := session.NewSession(&volcengine.Config{
		Region:      volcengine.String(stsConfig.Region),
		Credentials: credentials.NewStaticCredentials(stsConfig.AccessKey, stsConfig.SecretKey, ""),
		Endpoint:    volcengine.String(stsConfig.STSBaseURL),
	})
	if err != nil {
		http.Error(w, "创建云会话失败: "+err.Error(), http.StatusInternalServerError)
		return
	}
	// 创建STS服务客户端
	stsClient := sts.New(sess)
	// 准备AssumeRole请求
	roleSessionName := "serverless-aig-" + user.ID.String()
	assumeRoleInput := &sts.AssumeRoleInput{
		RoleTrn:         volcengine.String(stsConfig.RoleArn),
		RoleSessionName: volcengine.String(roleSessionName),
		DurationSeconds: volcengine.Int32(3600),
	}
	// 调用AssumeRole API
	assumeRoleOutput, err := stsClient.AssumeRole(assumeRoleInput)
	if err != nil {
		http.Error(w, "调用火山引擎STS AssumeRole服务失败: "+err.Error(), http.StatusInternalServerError)
		return
	}
	// 构建TOS临时访问凭证响应
	credentialsResp := TOSCredentialsResponse{
		Success: true,
		Credentials: TOSCredentials{
			AccessKeyID:     *assumeRoleOutput.Credentials.AccessKeyId,
			SecretAccessKey: *assumeRoleOutput.Credentials.SecretAccessKey,
			SessionToken:    *assumeRoleOutput.Credentials.SessionToken,
			Expiration:      *assumeRoleOutput.Credentials.ExpiredTime,
		},
		Bucket:           stsConfig.Bucket,
		Endpoint:         stsConfig.Endpoint,
		IntranetEndpoint: stsConfig.IntranetEndpoint,
		Region:           stsConfig.Region,
	}
	// 返回TOS临时访问凭证
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(credentialsResp)
}
