package main

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/sa<PERSON><PERSON>nov/go-openai"
)

// OpenAIClient 是OpenAI API客户端的包装器
type OpenAIClient struct {
	client *openai.Client
}

// ClientManager 管理不同类型的客户端实例
type ClientManager struct {
	bytedanceClient *OpenAIClient
	bytedanceOnce   sync.Once
	mu              sync.RWMutex
}

// 全局客户端管理器实例
var clientManager = &ClientManager{}

// getBytedanceClient 线程安全地获取字节跳动客户端
func (cm *ClientManager) getBytedanceClient() (*OpenAIClient, error) {
	var err error
	cm.bytedanceOnce.Do(func() {
		apiKey := GetBytedanceAPIKey()
		baseUrl := GetBytedanceBaseURL()
		// 检查配置是否可用
		if baseUrl == "" || apiKey == "" {
			err = errors.New("字节跳动API密钥或基础URL不能为空")
			return
		}
		// 创建客户端配置
		clientConfig := openai.DefaultConfig(apiKey)
		clientConfig.BaseURL = baseUrl
		// 创建客户端
		client := openai.NewClientWithConfig(clientConfig)
		cm.bytedanceClient = &OpenAIClient{
			client: client,
		}
	})
	if err != nil {
		return nil, err
	}
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	if cm.bytedanceClient == nil {
		return nil, errors.New("字节跳动客户端初始化失败")
	}
	return cm.bytedanceClient, nil
}



// ResetClients 重置所有客户端（在配置更改时使用）
func (cm *ClientManager) ResetClients() {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.bytedanceClient = nil
	cm.bytedanceOnce = sync.Once{}
}

// GetClientForModel 线程安全地获取适合模型的客户端
// 使用 sync.Once 确保了初始化代码只在第一次调用时执行一次
func GetClientForModel(model string) (*OpenAIClient, error) {
	// 根据模型选择客户端
	if containsKey(BYTEDANCE_MODELS_MAP, model) {
		return clientManager.getBytedanceClient()
	}
	return nil, errors.New("无法为该模型创建客户端")
}

// ResetAllClients 重置所有客户端的公共接口
func ResetAllClients() {
	clientManager.ResetClients()
}

// 发送聊天完成请求
func (c *OpenAIClient) CreateChatCompletion(
	ctx context.Context,
	model string,
	messages []openai.ChatCompletionMessage,
	temperature float32,
) (*openai.ChatCompletionResponse, error) {
	// 创建请求
	request := openai.ChatCompletionRequest{
		Model:       model,
		Messages:    messages,
		Temperature: temperature,
	}
	// 设置超时
	timeoutCtx, cancel := context.WithTimeout(ctx, 60*time.Second)
	defer cancel()
	// 发送请求
	response, err := c.client.CreateChatCompletion(timeoutCtx, request)
	if err != nil {
		return nil, fmt.Errorf("OpenAI API错误: %w", err)
	}
	return &response, nil
}

// 发送带有结构化输出的聊天完成请求
func (c *OpenAIClient) CreateChatCompletionWithResponseFormat(
	ctx context.Context,
	model string,
	messages []openai.ChatCompletionMessage,
	temperature float32,
	responseFormat *openai.ChatCompletionResponseFormat,
) (*openai.ChatCompletionResponse, error) {
	// 创建请求
	request := openai.ChatCompletionRequest{
		Model:       model,
		Messages:    messages,
		Temperature: temperature,
	}
	// 如果提供了响应格式，则设置
	if responseFormat != nil {
		request.ResponseFormat = responseFormat
	}
	// 设置超时
	timeoutCtx, cancel := context.WithTimeout(ctx, 60*time.Second)
	defer cancel()
	// 发送请求
	response, err := c.client.CreateChatCompletion(timeoutCtx, request)
	if err != nil {
		return nil, fmt.Errorf("OpenAI API错误: %w", err)
	}
	return &response, nil
}

// 发送带有图像的聊天完成请求
func (c *OpenAIClient) CreateChatCompletionWithImage(
	ctx context.Context,
	model string,
	systemPrompt string,
	userText string,
	imageURL string,
	temperature float32,
) (*openai.ChatCompletionResponse, error) {
	// 创建消息数组
	messages := []openai.ChatCompletionMessage{}
	if systemPrompt != "" {
		// 创建系统消息
		systemMessage := openai.ChatCompletionMessage{
			Role:    openai.ChatMessageRoleSystem,
			Content: systemPrompt,
		}
		messages = append(messages, systemMessage)
	}
	// 创建用户消息（包含文本和图像）
	userMessage := openai.ChatCompletionMessage{
		Role: openai.ChatMessageRoleUser,
		MultiContent: []openai.ChatMessagePart{
			{
				Type: openai.ChatMessagePartTypeImageURL,
				ImageURL: &openai.ChatMessageImageURL{
					URL:    imageURL,
					Detail: openai.ImageURLDetailHigh,
				},
			},
			{
				Type: openai.ChatMessagePartTypeText,
				Text: userText,
			},
		},
	}
	messages = append(messages, userMessage)
	// 发送请求
	return c.CreateChatCompletion(ctx, model, messages, temperature)
}

func (c *OpenAIClient) CreateChatCompletionWithText(
	ctx context.Context,
	model string,
	systemPrompt string,
	userText string,
	temperature float32,
) (*openai.ChatCompletionResponse, error) {
	// 创建系统消息
	systemMessage := openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleSystem,
		Content: systemPrompt,
	}
	// 创建用户消息
	userMessage := openai.ChatCompletionMessage{
		Role: openai.ChatMessageRoleUser,
		MultiContent: []openai.ChatMessagePart{
			{
				Type: openai.ChatMessagePartTypeText,
				Text: userText,
			},
		},
	}
	// 创建消息数组
	messages := []openai.ChatCompletionMessage{systemMessage, userMessage}
	// 发送请求
	return c.CreateChatCompletion(ctx, model, messages, temperature)
}

func CreateChatCompletion(ctx context.Context, chatRequest ChatRequest) (*openai.ChatCompletionResponse, error) {
	if chatRequest.ContentType == "text" {
		client, err := GetClientForModel(chatRequest.Model)
		if err != nil {
			return nil, err
		}
		res, err := client.CreateChatCompletionWithText(ctx, chatRequest.Model, chatRequest.PromptKey, chatRequest.Text, chatRequest.Temperature)
		if err != nil {
			return nil, err
		}
		return res, nil
	} else if chatRequest.ContentType == "image" {
		client, err := GetClientForModel(chatRequest.Model)
		if err != nil {
			return nil, err
		}
		res, err := client.CreateChatCompletionWithImage(ctx, chatRequest.Model, chatRequest.PromptKey, chatRequest.Text, chatRequest.Content, chatRequest.Temperature)
		if err != nil {
			return nil, err
		}
		return res, nil
	}
	return nil, errors.New("不支持的ContentType")
}

// 发送带有图像和结构化输出的聊天完成请求
func (c *OpenAIClient) CreateChatCompletionWithImageAndResponseFormat(
	ctx context.Context,
	model string,
	systemPrompt string,
	userText string,
	imageURL string,
	temperature float32,
	responseFormat *openai.ChatCompletionResponseFormat,
) (*openai.ChatCompletionResponse, error) {
	// 创建消息数组
	messages := []openai.ChatCompletionMessage{}
	if systemPrompt != "" {
		// 创建系统消息
		systemMessage := openai.ChatCompletionMessage{
			Role:    openai.ChatMessageRoleSystem,
			Content: systemPrompt,
		}
		messages = append(messages, systemMessage)
	}
	// 创建用户消息（包含文本和图像）
	userMessage := openai.ChatCompletionMessage{
		Role: openai.ChatMessageRoleUser,
		MultiContent: []openai.ChatMessagePart{
			{
				Type: openai.ChatMessagePartTypeImageURL,
				ImageURL: &openai.ChatMessageImageURL{
					URL:    imageURL,
					Detail: openai.ImageURLDetailHigh,
				},
			},
			{
				Type: openai.ChatMessagePartTypeText,
				Text: userText,
			},
		},
	}
	messages = append(messages, userMessage)
	// 发送请求
	return c.CreateChatCompletionWithResponseFormat(ctx, model, messages, temperature, responseFormat)
}

// 发送带有文本和结构化输出的聊天完成请求
func (c *OpenAIClient) CreateChatCompletionWithTextAndResponseFormat(
	ctx context.Context,
	model string,
	systemPrompt string,
	userText string,
	temperature float32,
	responseFormat *openai.ChatCompletionResponseFormat,
) (*openai.ChatCompletionResponse, error) {
	// 创建系统消息
	systemMessage := openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleSystem,
		Content: systemPrompt,
	}
	// 创建用户消息
	userMessage := openai.ChatCompletionMessage{
		Role: openai.ChatMessageRoleUser,
		MultiContent: []openai.ChatMessagePart{
			{
				Type: openai.ChatMessagePartTypeText,
				Text: userText,
			},
		},
	}
	// 创建消息数组
	messages := []openai.ChatCompletionMessage{systemMessage, userMessage}
	// 发送请求
	return c.CreateChatCompletionWithResponseFormat(ctx, model, messages, temperature, responseFormat)
}
