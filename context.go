package main

import (
	"context"

	"github.com/supabase-community/auth-go/types"
)

// 定义上下文键类型
type contextKey string

// 用户上下文键
const userContextKey contextKey = "user"

// WithUser 将用户信息添加到上下文中
func WithUser(ctx context.Context, user *types.UserResponse) context.Context {
	return context.WithValue(ctx, userContextKey, user)
}

// UserFromContext 从上下文中获取用户信息
func UserFromContext(ctx context.Context) (*types.UserResponse, bool) {
	user, ok := ctx.Value(userContextKey).(*types.UserResponse)
	return user, ok
}
