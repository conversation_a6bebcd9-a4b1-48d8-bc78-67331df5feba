# turso_to_postgresql

## 第一步：从 Turso 导出数据

```bash
# 安装 Turso CLI (如果未安装)
curl -sSfL https://get.tur.so/install.sh | bash
# 登录到 Turso
turso auth login --headless

# 导出数据库
# 方法一：使用 Turso CLI export 命令
turso db export aig
# ------------
# 方法二：使用 Turso CLI db shell 命令
turso db shell <db-name> .dump > dump.sql
# 将 SQL 文件转换为 SQLite 数据库文件
sqlite3 <db-name>.db < dump.sql
```

## 第二步：使用 pgloader 迁移到 PostgreSQL

```bash
# 安装 pgloader (Debian/Ubuntu)
sudo apt-get install pgloader
# 迁移命令
pgloader \
  --verbose \
  --type sqlite \
  output.db \  # 或 mydb.db
  postgresql://用户名:密码@服务器地址:端口/数据库名
```

## 第三步：给新用户分配权限

```sql
-- 创建新用户
CREATE USER aig_user WITH PASSWORD '<password>';
-- 授予数据库所有权限
GRANT ALL PRIVILEGES ON DATABASE aig TO aig_user;
-- 授予所有表的所有权限（需在目标数据库执行）
\c aig  -- 切换到目标数据库
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO aig_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO aig_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO aig_user;
-- 确保未来新建对象也有权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO aig_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO aig_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO aig_user;
```

## 导出整个 Schema 的结构（不含数据）

```bash
pg_dump -h localhost -U aig_user -n <schema名> --schema-only -d aig > public_schema.sql
```