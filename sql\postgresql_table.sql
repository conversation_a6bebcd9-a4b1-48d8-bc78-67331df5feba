BEGIN;
-- 创建网站配置表
CREATE TABLE IF NOT EXISTS website_configs (
    id TEXT PRIMARY KEY,          -- 网站业务ID (如'dongni100')
    name TEXT NOT NULL,            -- 网站名称
    url TEXT NOT NULL,             -- 网站URL
    actions JSONB NOT NULL         -- 操作步骤数组 (JSON格式)
);

CREATE TABLE IF NOT EXISTS system_prompts (
    id SERIAL PRIMARY KEY,
    prompt_key TEXT NOT NULL UNIQUE, -- 提示词的唯一标识符
    prompt_text TEXT NOT NULL, -- 提示词内容
    description TEXT, -- 提示词描述
    category TEXT, -- 提示词分类
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL, -- 创建时间戳
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL -- 更新时间戳
);

-- 添加UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- 创建充值状态枚举类型
CREATE TYPE IF NOT EXISTS approval_status AS ENUM (
    'pending',   -- 待处理
    'approved',  -- 已批准
    'rejected'   -- 已拒绝
);
CREATE TABLE IF NOT EXISTS recharge_requests (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,-- 对应于Supabase中的auth.users.id
    amount INTEGER NOT NULL, -- 充值金额
    order_id TEXT NOT NULL UNIQUE, -- 支付宝订单号，唯一键防止重复充值
    status  approval_status NOT NULL DEFAULT 'pending', -- 充值状态: pending, approved, rejected
    payment_method TEXT NOT NULL DEFAULT 'alipay'::text, -- 支付方式：默认支付宝
    payment_proof TEXT, -- 支付凭证URL或路径
    admin_note TEXT, -- 管理员处理备注
    processed_by TEXT, -- 处理此申请的管理员ID
    processed_at TIMESTAMPTZ, -- 处理时间
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL, -- 创建时间戳 ,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL -- 更新时间戳
);

CREATE TYPE IF NOT EXISTS transaction_type AS ENUM (
    'deposit',   -- 充值
    'consumption' -- 消费
);
CREATE TABLE IF NOT EXISTS transactions (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL, -- 对应于Supabase中的auth.users.id
    type transaction_type NOT NULL, -- "deposit"用于增加积分，"consumption"用于使用积分
    amount INTEGER NOT NULL, -- 交易积分数量
    balance_after INTEGER NOT NULL, -- 本次交易后的积分余额
    description TEXT, -- 交易描述
    metadata TEXT, -- 带有附加交易元数据的JSON字符串
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL -- 创建时间戳
);

CREATE TABLE IF NOT EXISTS wallets (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL UNIQUE,-- 对应于Supabase中的auth.users.id
    balance INTEGER NOT NULL DEFAULT 0,-- 当前钱包积分余额（整数）
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,-- 创建时间戳
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL-- 最后更新时间戳
);
-- 添加索引优化查询
CREATE INDEX idx_recharge_requests_user_id ON recharge_requests(user_id);
CREATE INDEX idx_transactions_user_id ON transactions(user_id);
CREATE INDEX idx_wallets_user_id ON wallets(user_id);
-- 添加检查约束确保数据完整性
ALTER TABLE recharge_requests ADD CONSTRAINT chk_amount_positive CHECK (amount > 0);
ALTER TABLE transactions ADD CONSTRAINT chk_amount_positive CHECK (amount > 0);
ALTER TABLE wallets ADD CONSTRAINT chk_balance_non_negative CHECK (balance >= 0);
COMMIT;